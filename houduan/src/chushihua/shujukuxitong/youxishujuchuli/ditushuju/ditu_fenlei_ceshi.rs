#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::ditu_fenlei_chaxun::ditu_fenlei_chaxun;
use super::ditushujujiegouti::{ditu_fenlei_chaxun_canshu, ditu_fenlei_tiaojian};
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use std::fs::OpenOptions;
use std::io::Write;

/// 地图分类查询测试类
pub struct ditu_fenlei_ceshi;

impl ditu_fenlei_ceshi {
    /// 完整的地图分类查询功能测试
    pub async fn wanzheng_fenlei_ceshi(
        mysql_guanli: mysql_lianjie_guanli,
        redis_guanli: Option<redis_lianjie_guanli>,
    ) -> anyhow::Result<()> {
        let mut log_neirong = String::new();
        
        Self::tianjia_log(&mut log_neirong, &format!("{}", "=".repeat(80)));
        Self::tianjia_log(&mut log_neirong, "开始完整的地图分类查询功能测试");
        Self::tianjia_log(&mut log_neirong, &format!("{}", "=".repeat(80)));

        // 创建分类查询管理器
        let fenlei_chaxun = match redis_guanli {
            Some(redis) => {
                Self::tianjia_log(&mut log_neirong, "✓ 使用Redis缓存模式创建地图分类查询管理器");
                ditu_fenlei_chaxun::new_with_redis(mysql_guanli, redis)
            }
            None => {
                Self::tianjia_log(&mut log_neirong, "✓ 使用无缓存模式创建地图分类查询管理器");
                ditu_fenlei_chaxun::new(mysql_guanli)
            }
        };

        // 测试1: 单个分类条件查询
        Self::tianjia_log(&mut log_neirong, &format!("\n{}", "=".repeat(60)));
        Self::tianjia_log(&mut log_neirong, "测试1: 单个分类条件查询");
        Self::tianjia_log(&mut log_neirong, &format!("{}", "=".repeat(60)));
        Self::ceshi_dange_fenlei_tiaojian(&fenlei_chaxun, &mut log_neirong).await?;

        // 测试2: 多个分类条件AND查询
        Self::tianjia_log(&mut log_neirong, &format!("\n{}", "=".repeat(60)));
        Self::tianjia_log(&mut log_neirong, "测试2: 多个分类条件AND查询");
        Self::tianjia_log(&mut log_neirong, &format!("{}", "=".repeat(60)));
        Self::ceshi_duoge_fenlei_and_chaxun(&fenlei_chaxun, &mut log_neirong).await?;

        // 测试3: 多个分类条件OR查询
        Self::tianjia_log(&mut log_neirong, &format!("\n{}", "=".repeat(60)));
        Self::tianjia_log(&mut log_neirong, "测试3: 多个分类条件OR查询");
        Self::tianjia_log(&mut log_neirong, &format!("{}", "=".repeat(60)));
        Self::ceshi_duoge_fenlei_or_chaxun(&fenlei_chaxun, &mut log_neirong).await?;

        // 测试4: Redis缓存性能测试
        Self::tianjia_log(&mut log_neirong, &format!("\n{}", "=".repeat(60)));
        Self::tianjia_log(&mut log_neirong, "测试4: Redis缓存性能测试");
        Self::tianjia_log(&mut log_neirong, &format!("{}", "=".repeat(60)));
        Self::ceshi_redis_huancun_xingneng(&fenlei_chaxun, &mut log_neirong).await?;

        // 测试5: 缓存清除功能测试
        Self::tianjia_log(&mut log_neirong, &format!("\n{}", "=".repeat(60)));
        Self::tianjia_log(&mut log_neirong, "测试5: 缓存清除功能测试");
        Self::tianjia_log(&mut log_neirong, &format!("{}", "=".repeat(60)));
        Self::ceshi_huancun_qingchu(&fenlei_chaxun, &mut log_neirong).await?;

        // 测试6: 边界条件测试
        Self::tianjia_log(&mut log_neirong, &format!("\n{}", "=".repeat(60)));
        Self::tianjia_log(&mut log_neirong, "测试6: 边界条件测试");
        Self::tianjia_log(&mut log_neirong, &format!("{}", "=".repeat(60)));
        Self::ceshi_bianjie_tiaojian(&fenlei_chaxun, &mut log_neirong).await?;

        Self::tianjia_log(&mut log_neirong, &format!("\n{}", "=".repeat(80)));
        Self::tianjia_log(&mut log_neirong, "完整的地图分类查询功能测试完成！");
        Self::tianjia_log(&mut log_neirong, &format!("{}", "=".repeat(80)));

        // 保存日志到文件
        Self::baocun_log_wenjian(&log_neirong).await?;

        // 同时输出到控制台
        println!("{}", log_neirong);

        Ok(())
    }

    /// 测试单个分类条件查询
    async fn ceshi_dange_fenlei_tiaojian(
        fenlei_chaxun: &ditu_fenlei_chaxun,
        log_neirong: &mut String,
    ) -> anyhow::Result<()> {
        Self::tianjia_log(log_neirong, "子测试1.1: 查询城镇类型地图（leixing_town=true）");
        
        let tiaojian = vec![ditu_fenlei_tiaojian::new("leixing_town".to_string(), true)];
        let canshu = ditu_fenlei_chaxun_canshu::new(5, 1, tiaojian, "AND".to_string());

        let kaishi_shijian = std::time::Instant::now();
        match fenlei_chaxun.chaxun_fenlei_ditu_liebiao(canshu).await {
            Ok(jieguo) => {
                let yongshi = kaishi_shijian.elapsed();
                Self::tianjia_log(log_neirong, &format!("✓ 查询耗时: {:?}", yongshi));
                Self::tianjia_log(log_neirong, &format!("✓ 总共找到{}个城镇类型地图", jieguo.zonggong_ditu_shuliang));
                Self::tianjia_log(log_neirong, &format!("✓ 当前页显示{}个地图", jieguo.dangqian_ye_ditu_shuliang));
                Self::tianjia_log(log_neirong, "原始完整返回结果:");
                Self::tianjia_log(log_neirong, &format!("{:#?}", jieguo));
            }
            Err(e) => {
                Self::tianjia_log(log_neirong, &format!("✗ 查询失败: {}", e));
            }
        }

        Self::tianjia_log(log_neirong, "\n子测试1.2: 查询野外类型地图（leixing_field=true）");
        
        let tiaojian = vec![ditu_fenlei_tiaojian::new("leixing_field".to_string(), true)];
        let canshu = ditu_fenlei_chaxun_canshu::new(3, 1, tiaojian, "AND".to_string());

        let kaishi_shijian = std::time::Instant::now();
        match fenlei_chaxun.chaxun_fenlei_ditu_liebiao(canshu).await {
            Ok(jieguo) => {
                let yongshi = kaishi_shijian.elapsed();
                Self::tianjia_log(log_neirong, &format!("✓ 查询耗时: {:?}", yongshi));
                Self::tianjia_log(log_neirong, &format!("✓ 总共找到{}个野外类型地图", jieguo.zonggong_ditu_shuliang));
                Self::tianjia_log(log_neirong, &format!("✓ 当前页显示{}个地图", jieguo.dangqian_ye_ditu_shuliang));
                Self::tianjia_log(log_neirong, "原始完整返回结果:");
                Self::tianjia_log(log_neirong, &format!("{:#?}", jieguo));
            }
            Err(e) => {
                Self::tianjia_log(log_neirong, &format!("✗ 查询失败: {}", e));
            }
        }

        Ok(())
    }

    /// 测试多个分类条件AND查询
    async fn ceshi_duoge_fenlei_and_chaxun(
        fenlei_chaxun: &ditu_fenlei_chaxun,
        log_neirong: &mut String,
    ) -> anyhow::Result<()> {
        Self::tianjia_log(log_neirong, "子测试2.1: 查询既是城镇又有怪物的地图（leixing_town=true AND you_guaiwu=true）");
        
        let tiaojian = vec![
            ditu_fenlei_tiaojian::new("leixing_town".to_string(), true),
            ditu_fenlei_tiaojian::new("you_guaiwu".to_string(), true),
        ];
        let canshu = ditu_fenlei_chaxun_canshu::new(5, 1, tiaojian, "AND".to_string());

        let kaishi_shijian = std::time::Instant::now();
        match fenlei_chaxun.chaxun_fenlei_ditu_liebiao(canshu).await {
            Ok(jieguo) => {
                let yongshi = kaishi_shijian.elapsed();
                Self::tianjia_log(log_neirong, &format!("✓ 查询耗时: {:?}", yongshi));
                Self::tianjia_log(log_neirong, &format!("✓ 总共找到{}个符合条件的地图", jieguo.zonggong_ditu_shuliang));
                Self::tianjia_log(log_neirong, &format!("✓ 当前页显示{}个地图", jieguo.dangqian_ye_ditu_shuliang));
                Self::tianjia_log(log_neirong, "原始完整返回结果:");
                Self::tianjia_log(log_neirong, &format!("{:#?}", jieguo));
            }
            Err(e) => {
                Self::tianjia_log(log_neirong, &format!("✗ 查询失败: {}", e));
            }
        }

        Ok(())
    }

    /// 测试多个分类条件OR查询
    async fn ceshi_duoge_fenlei_or_chaxun(
        fenlei_chaxun: &ditu_fenlei_chaxun,
        log_neirong: &mut String,
    ) -> anyhow::Result<()> {
        Self::tianjia_log(log_neirong, "子测试3.1: 查询城镇或地下城类型的地图（leixing_town=true OR leixing_dungeon=true）");
        
        let tiaojian = vec![
            ditu_fenlei_tiaojian::new("leixing_town".to_string(), true),
            ditu_fenlei_tiaojian::new("leixing_dungeon".to_string(), true),
        ];
        let canshu = ditu_fenlei_chaxun_canshu::new(8, 1, tiaojian, "OR".to_string());

        let kaishi_shijian = std::time::Instant::now();
        match fenlei_chaxun.chaxun_fenlei_ditu_liebiao(canshu).await {
            Ok(jieguo) => {
                let yongshi = kaishi_shijian.elapsed();
                Self::tianjia_log(log_neirong, &format!("✓ 查询耗时: {:?}", yongshi));
                Self::tianjia_log(log_neirong, &format!("✓ 总共找到{}个符合条件的地图", jieguo.zonggong_ditu_shuliang));
                Self::tianjia_log(log_neirong, &format!("✓ 当前页显示{}个地图", jieguo.dangqian_ye_ditu_shuliang));
                Self::tianjia_log(log_neirong, "原始完整返回结果:");
                Self::tianjia_log(log_neirong, &format!("{:#?}", jieguo));
            }
            Err(e) => {
                Self::tianjia_log(log_neirong, &format!("✗ 查询失败: {}", e));
            }
        }

        Ok(())
    }

    /// 测试Redis缓存性能
    async fn ceshi_redis_huancun_xingneng(
        fenlei_chaxun: &ditu_fenlei_chaxun,
        log_neirong: &mut String,
    ) -> anyhow::Result<()> {
        let tiaojian = vec![ditu_fenlei_tiaojian::new("leixing_field".to_string(), true)];
        let canshu = ditu_fenlei_chaxun_canshu::new(6, 1, tiaojian, "AND".to_string());

        Self::tianjia_log(log_neirong, "子测试4.1: 第一次查询（从数据库获取，建立缓存）");
        let kaishi_shijian1 = std::time::Instant::now();
        match fenlei_chaxun.chaxun_fenlei_ditu_liebiao(canshu.clone()).await {
            Ok(jieguo) => {
                let yongshi1 = kaishi_shijian1.elapsed();
                Self::tianjia_log(log_neirong, &format!("✓ 第一次查询耗时: {:?}", yongshi1));
                Self::tianjia_log(log_neirong, &format!("✓ 获取到{}个地图", jieguo.dangqian_ye_ditu_shuliang));
                Self::tianjia_log(log_neirong, "原始完整返回结果:");
                Self::tianjia_log(log_neirong, &format!("{:#?}", jieguo));
            }
            Err(e) => {
                Self::tianjia_log(log_neirong, &format!("✗ 第一次查询失败: {}", e));
            }
        }

        Self::tianjia_log(log_neirong, "\n子测试4.2: 第二次查询（从Redis缓存获取）");
        let kaishi_shijian2 = std::time::Instant::now();
        match fenlei_chaxun.chaxun_fenlei_ditu_liebiao(canshu).await {
            Ok(jieguo) => {
                let yongshi2 = kaishi_shijian2.elapsed();
                Self::tianjia_log(log_neirong, &format!("✓ 第二次查询耗时: {:?}", yongshi2));
                Self::tianjia_log(log_neirong, &format!("✓ 获取到{}个地图", jieguo.dangqian_ye_ditu_shuliang));
                Self::tianjia_log(log_neirong, "原始完整返回结果:");
                Self::tianjia_log(log_neirong, &format!("{:#?}", jieguo));
            }
            Err(e) => {
                Self::tianjia_log(log_neirong, &format!("✗ 第二次查询失败: {}", e));
            }
        }

        Ok(())
    }

    /// 测试缓存清除功能
    async fn ceshi_huancun_qingchu(
        fenlei_chaxun: &ditu_fenlei_chaxun,
        log_neirong: &mut String,
    ) -> anyhow::Result<()> {
        Self::tianjia_log(log_neirong, "子测试5.1: 清除所有分类查询缓存");
        
        match fenlei_chaxun.qingchu_fenlei_huancun().await {
            Ok(shuliang) => {
                Self::tianjia_log(log_neirong, &format!("✓ 成功清除{}个分类查询缓存", shuliang));
            }
            Err(e) => {
                Self::tianjia_log(log_neirong, &format!("✗ 清除缓存失败: {}", e));
            }
        }

        Ok(())
    }

    /// 测试边界条件
    async fn ceshi_bianjie_tiaojian(
        fenlei_chaxun: &ditu_fenlei_chaxun,
        log_neirong: &mut String,
    ) -> anyhow::Result<()> {
        Self::tianjia_log(log_neirong, "子测试6.1: 空条件查询（应该返回所有地图）");
        
        let tiaojian = vec![];
        let canshu = ditu_fenlei_chaxun_canshu::new(5, 1, tiaojian, "AND".to_string());

        match fenlei_chaxun.chaxun_fenlei_ditu_liebiao(canshu).await {
            Ok(jieguo) => {
                Self::tianjia_log(log_neirong, &format!("✓ 空条件查询成功，总共{}个地图", jieguo.zonggong_ditu_shuliang));
                Self::tianjia_log(log_neirong, "原始完整返回结果:");
                Self::tianjia_log(log_neirong, &format!("{:#?}", jieguo));
            }
            Err(e) => {
                Self::tianjia_log(log_neirong, &format!("✗ 空条件查询失败: {}", e));
            }
        }

        Self::tianjia_log(log_neirong, "\n子测试6.2: 无效字段名查询");
        
        let tiaojian = vec![ditu_fenlei_tiaojian::new("invalid_field".to_string(), true)];
        let canshu = ditu_fenlei_chaxun_canshu::new(5, 1, tiaojian, "AND".to_string());

        match fenlei_chaxun.chaxun_fenlei_ditu_liebiao(canshu).await {
            Ok(jieguo) => {
                Self::tianjia_log(log_neirong, &format!("✓ 无效字段查询结果，总共{}个地图", jieguo.zonggong_ditu_shuliang));
                Self::tianjia_log(log_neirong, "原始完整返回结果:");
                Self::tianjia_log(log_neirong, &format!("{:#?}", jieguo));
            }
            Err(e) => {
                Self::tianjia_log(log_neirong, &format!("✗ 无效字段查询失败: {}", e));
            }
        }

        Ok(())
    }

    /// 添加日志内容
    fn tianjia_log(log_neirong: &mut String, neirong: &str) {
        log_neirong.push_str(neirong);
        log_neirong.push('\n');
    }

    /// 保存日志到文件
    async fn baocun_log_wenjian(log_neirong: &str) -> anyhow::Result<()> {
        let riqi = chrono::Local::now().format("%Y-%m-%d").to_string();
        let shijian = chrono::Local::now().format("%H-%M-%S").to_string();
        let wenjian_ming = format!("rizhi/ditu_fenlei_ceshi_{}_{}.log", riqi, shijian);

        let mut wenjian = OpenOptions::new()
            .create(true)
            .write(true)
            .append(true)
            .open(&wenjian_ming)?;

        writeln!(wenjian, "{}", log_neirong)?;
        println!("✓ 测试日志已保存到: {}", wenjian_ming);

        Ok(())
    }
}
